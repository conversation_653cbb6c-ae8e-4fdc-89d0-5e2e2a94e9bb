
import React = require("react");
import { Items, UnknownItem } from "src/enums/resources";
import { startProgressBar } from "src/gameinfo";
import { ItemIcon } from "./common";
import { getText } from "src/i18n";
import { ResourceMetaInfo } from "src/Interfaces";
import { FishingButton } from "./FishingButton";
import { useRootStore } from "../stores/rootStore";
import { GroundPanel } from "./GroundGrid";
import { activeOverlays, batchShowPromptOverlay, showFadedOverlay, showPromptOverlay } from "src/util";
import { MacOSModal } from "./WindowManagement/MacOSModal";
import { BuildList } from "./BuildList";
import { useGroundStore } from "src/stores/groundStore";
import { ItemDescriptionPopover } from "./ItemDescriptionPopover";
import { useLocationStore } from "src/stores/locationStore";

const ResourceIcon = ({resource}) => {
    return (
        <div className={`resource-icon-inner ${resource.rarity}`}>
            <ItemIcon itemDef={resource} />
        </div>
    )
}

const ResourcesPanel = (props : {
    resourceMetaInfos: ResourceMetaInfo[],
}) => {
    const [selectedResource, setSelectedResource] = React.useState(null);
    const [popOverRect, setPopOverRect] = React.useState<DOMRect | null>(null);

    const onResourceHover = (e: React.MouseEvent, resource) => {
        // Prevent event bubbling
        e.stopPropagation();
        // Store the bounding rectangle of the selected item for popover positioning
        const element = e.currentTarget.closest('.resource-icon'); //.closest('.inventory-item');
        console.log("Resource element:", element, element.getBoundingClientRect());
        // Get the bounding rectangle relative to the viewport
        const rect = element.getBoundingClientRect();

        // Set the rect first, then the selected stack to ensure the popover has the position
        // before it tries to render
        setPopOverRect(rect);

        setSelectedResource(resource);
        // // Use a small timeout to ensure the rect is set before the popover renders
        // setTimeout(() => {
        //     setSelectedResource(resource);
        // }, 0);
    };

    // Check if resourceMetaInfos is undefined or null
    const metaInfos = props.resourceMetaInfos || [];

    return (
        <>
            <div className="resource-grid-container">
                <div className="subtitle-label">
                    <div>
                        <img src = "images/ui_icons/cube.svg" className="resource-cube-icon" />
                        {getText("AVAILABLE RESOURCES")}
                    </div>
                </div>
                
                <div className="scrollable-container resource-grid">
                    {metaInfos.length === 0 && <p className="no-resources">No resources available in this area</p>}
                    {metaInfos.map((resourceMeta, index) => {
                        const resource = resourceMeta.discovered? Items[resourceMeta.id] : UnknownItem;
                        if (!resource) {
                            console.error("Resource not found!!!!!!!!!!!!", resourceMeta.id);
                        }
                        return (
                            <div key={index}
                                className={`resource-icon ${selectedResource?.id === resourceMeta.id ? 'selected' : ''}`}
                                // onClick={() => setSelectedResource(resource)}
                                onClick={(e) => onResourceHover(e, resource)}
                                onMouseEnter={(e) => onResourceHover(e, resource)}
                                onMouseLeave={() => {
                                    setSelectedResource(null);
                                    setPopOverRect(null);
                                }}
                            >
                                <ResourceIcon resource={resource}/>
                            </div>
                        )
                    })}
                </div>
            </div>

            {selectedResource && (
                <ItemDescriptionPopover
                    itemDef={selectedResource}
                    anchorRect={popOverRect}
                    isOpen={!!selectedResource}
                    onClose={() => {
                        setSelectedResource(null);
                        setPopOverRect(null);
                    }}
                />
            )}
        </>
    )
};

export const EnvironmentDisplay = React.memo((props : {
    terrainName: string,
}) => {
    const currentLocationBuildingUuid = useLocationStore(state => state.currentLocationBuildingUuid);
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    const resourceMetaInfosInAllRegions = useRootStore(state => state.resourceMetaInfosInAllRegions);
    // const resourcesMetaInfo = useRootStore(state => state.resourceMetaInfosInAllRegions[currRegionIndex]);
    // const biomesList = useRootStore(state => state.biomesList);


    // Add null check to ensure resourceMetaInfosInAllRegions and currRegionIndex are valid
    const resourcesMetaInfo: ResourceMetaInfo[] =
        resourceMetaInfosInAllRegions &&
        currRegionIndex !== undefined &&
        currRegionIndex !== null &&
        resourceMetaInfosInAllRegions[currRegionIndex]
            ? resourceMetaInfosInAllRegions[currRegionIndex]
            : [];

    console.log("ENviromentDisplay rerendered!!!");

    return (
        <div id="groundWrapper">
            {!currentLocationBuildingUuid && (
                <div id="environmentDisplay">
                    <div className="subtitle-label">
                        <div>
                            <img src = "images/ui_icons/safari.svg" className="resource-cube-icon" />
                            {getText("AVAILABLE ACTIVITIES")}
                        </div>
                    </div>
                    <div id="actionBtnsContainer">
                        <ExploreBtn resourcesMetaInfo={resourcesMetaInfo} resourceMetaInfosInAllRegions={resourceMetaInfosInAllRegions} />
                        <FishingButton />
                        <BuildBtn />
                        <div className='panel-btn' >
                            <img src="images/ui_icons/tools.svg" width={14} height={14} style={{filter: 'invert(1)'}} />
                            <div className="panel-btn-text">{getText("Hunt")}</div>
                        </div>
                    </div>
                    <div id="resources-panel">
                        <ResourcesPanel
                            resourceMetaInfos={resourcesMetaInfo}
                        />
                    </div>
                </div>
            )}

            <GroundPanel currentLocationBuildingUuid={currentLocationBuildingUuid}/>

        </div>
   );
});

export const BuildBtn = () => {

    const [openCraftModal, setOpenCraftModal] = React.useState(false);

    return <>
            <div className='panel-btn' onClick={() => setOpenCraftModal(true)}>
                <img src="images/ui_icons/building.png" width={14} height={14} style={{filter: 'invert(1)'}} />
                <div className="panel-btn-text">{getText("Build")}</div>
            </div>
            {openCraftModal && 
                <MacOSModal
                    title={getText('Build')}
                    isOpen={true}
                    onClose={() => setOpenCraftModal(false)}
                    initialSize={{ width: 600, height: 700 }}
                    // portalId={interactionModalId.current}
                >
                    <BuildList />
                </MacOSModal>
            }
    </>
}


const ExploreBtn = (props : {
    resourcesMetaInfo: ResourceMetaInfo[],
    resourceMetaInfosInAllRegions: ResourceMetaInfo[][],
}) => {

    const currRegionIndex = useRootStore(state => state.currRegionIndex);     // Get the current region index
    const setResourceMetaInfosInAllRegions = useRootStore(state => state.setResourceMetaInfosInAllRegions);
    const addItemDefsToGroundStacks = useGroundStore(state => state.addItemDefsToGroundStacks);
    const addToGroundBuildings = useGroundStore(state => state.addToGroundBuildings);

    const exploreBiome = () => {
        if (!props.resourcesMetaInfo || props.resourcesMetaInfo.length === 0) {
            console.warn("No resources available to explore");
            return;
        }

        startProgressBar(getText("Exploring"), 60, () => {
            let hasNewDiscovery = false;
            const discoveredResourceMetas = []
            for (let i = 0; i < 3; i++) {
                const discoveredResourceMeta = props.resourcesMetaInfo[Math.floor(Math.random() * props.resourcesMetaInfo.length)];
                discoveredResourceMetas.push(discoveredResourceMeta);
                // console.log("discoveredResourceMeta", discoveredResourceMeta);
                if (discoveredResourceMeta && !discoveredResourceMeta.discovered) {
                    discoveredResourceMeta.discovered = true;
                    hasNewDiscovery = true;
                }
            }

            if (hasNewDiscovery) {
                setResourceMetaInfosInAllRegions([...props.resourceMetaInfosInAllRegions]);
            }

            // if (discoveredResourceMeta && discoveredResourceMeta.id) {
            console.log("Exploring... discoveredResourceMetas", discoveredResourceMetas);
            const itemDefs = discoveredResourceMetas.map((resourceMeta) => Items[resourceMeta.id]);

            const buildingItemDefs = itemDefs.filter((itemDef) => itemDef.type.id === 'Building');
            if (buildingItemDefs) {
                buildingItemDefs.forEach((itemDef) => addToGroundBuildings(currRegionIndex, itemDef));     // Add buildings to ground buildings array directly instead of adding to ground stacks array.
            }
            
            const nonBuildingItemDefs = itemDefs.filter((itemDef) => itemDef.type.id !== 'Building');
            addItemDefsToGroundStacks(currRegionIndex, nonBuildingItemDefs);

            batchShowPromptOverlay(itemDefs.map((itemDef) => `+ ${itemDef.name} x${itemDef.quantityMod ?? 1}`));
        })
    }

    return (
        <div className="panel-btn" onClick={exploreBiome}>
            <img src="images/ui_icons/search.svg" width={14} height={14} style={{filter: 'invert(1)'}} />
            <div className="panel-btn-text">{getText("Search")}</div>
        </div>
    )
}

// const ActivityBtn = (onClick, label) => {
//     return (
//         <button className="panel-btn" onClick={onClick}>
//             <img src="images/ui_icons/search.svg" width={14} height={14} style={{filter: 'invert(1)'}} />
//             <div className="panel-btn-text">{label}</div>
//         </button>
//     );
// }
