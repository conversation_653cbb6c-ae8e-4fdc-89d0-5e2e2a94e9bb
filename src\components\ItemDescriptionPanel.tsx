import React = require("react");
import { ItemLocation, ResourceTypes, statsIconMap } from "src/enums/common_enum";
import { ItemIcon } from "./common";
import { useEquipmentStore } from "src/stores/equipmentStore";
import { useRootStore } from "src/stores/rootStore";
import { getText } from "src/i18n";
import { startProgressBar } from "src/gameinfo";
import { InventoryItemStack, Item } from "src/Interfaces";
import { useGroundStore } from "src/stores/groundStore";
import { MacOSModal } from "./WindowManagement/MacOSModal";
import { useLocationStore } from "src/stores/locationStore";


function getSignFromNumber(num: number): string {
    return num >= 0 ? '+' : '-';
}

interface StatLineProps {
    stat: number;
    type: string;
}

const StatLine: React.FC<StatLineProps> = ({stat, type}) => {
    if (!stat) {
        return null;
    }

    return (
        <div className="stat-line">
            <ItemIcon itemDef={{icon: statsIconMap[type]}} maxWidth={18} invertColor={true} />
            {getSignFromNumber(stat)}{stat}
            <br/>
        </div>
    );
};

interface ItemDescriptionPanelProps {
    selectedStack?: InventoryItemStack;
    onClose?: () => void;
    location?: ItemLocation;
    itemDef: Item;
}

export const ItemDescriptionPanel: React.FC<ItemDescriptionPanelProps> = ({
    selectedStack,
    onClose,
    location,
    itemDef
}) => {

    return (
        <>
            <div className="selected-resource-header">
                <ItemIcon itemDef={itemDef} />
                <span className="resource-name">{itemDef.name}</span>
            </div>
            {/* <div className="resource-type">
                {itemDef.type.name} {itemDef.type.icon}
            </div> */}
            <div className="resource-stats">
                {itemDef.description || 'No description available'}
            </div>

            {/* Show equipment slot type if it's an equipable item */}
            {itemDef.type.id === 'Equipable' && itemDef.slotType && (
                <div className="equipment-info">
                    <div className="slot-type">
                        <span className="slot-icon">{itemDef.slotType.icon}</span>
                        <span className="slot-name">{getText("Slot")}: {itemDef.slotType.name}</span>
                    </div>
                </div>
            )}

            {/* Show food stats if it's an edible item */}
            {(itemDef.type === ResourceTypes.EDIBLE || itemDef.type === ResourceTypes.MEDICINAL) &&
                <div>
                    <StatLine stat={itemDef.food} type="food" />
                    <StatLine stat={itemDef.water} type="water" />
                    <StatLine stat={itemDef.energy} type="energy" />
                    <StatLine stat={itemDef.health} type="health" />
                </div>
            }

            {/* Action buttons */}
            {selectedStack && (
                <div className="item-actions">
                    {/* Equip button for equipable items */}
                    {itemDef.type.id === 'Equipable' && (
                        <EquipButton selectedStack={selectedStack} onClose={onClose} />
                    )}

                    {/* Eat button for food items */}
                    {itemDef.type === ResourceTypes.EDIBLE && (
                        <EatButton selectedStack={selectedStack} onClose={onClose} />
                    )}

                    {/* button to get in a building */}
                    {itemDef.canHaveBuildings && (
                        <EnterButton selectedStack={selectedStack} onClose={onClose} />
                    )}

                    {(ItemLocation.Ground === location) &&
                        <PickUpButton selectedStack={selectedStack} onClose={onClose} />
                    }

                    {(ItemLocation.Inventory === location) &&
                        <DropButton selectedStack={selectedStack} onClose={onClose} />
                    }

                    {itemDef.id.endsWith('Tree') && (
                        <ChopButton selectedStack={selectedStack} onClose={onClose} />
                    )}
                    
                </div>
            )}
        </>
    );
};

const ChopButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    return (
        <div className="panel-btn" onClick={props.onClose}>
            <ItemIcon itemDef={{icon: 'images/ui_icons/chop.png'}} maxWidth={18} />
            {getText("Chop")}
        </div>
    );
};

const EnterButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {

    const setCurrentLocationBuildingUuid = useLocationStore(state => state.setCurrentLocationBuildingUuid);
    const handleEnter = () => {
        startProgressBar(getText("Entering"), 2, () => {
            setCurrentLocationBuildingUuid(props.selectedStack.uuid);
        });
        if (props.onClose) props.onClose();
    };

    return (
        <div className="panel-btn" onClick={handleEnter}>
            <ItemIcon itemDef={{icon: 'images/ui_icons/enter.png'}} maxWidth={18} />
            {getText("Enter")}
        </div>
    );
};

const EatButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    const removeItemsFromInventory = useRootStore(state => state.removeItemsFromInventory);

    const handleEat = () => {
        // Implement eat functionality here
        console.log("Eating", props.selectedStack.itemDef.name);
        startProgressBar(getText("Eating..."), 5, () => {
            removeItemsFromInventory([{itemDef: props.selectedStack.itemDef, uuid: props.selectedStack.uuid, quantity: 1}]);
        })
        if (props.onClose) props.onClose();
    };

    return (
        <div className="panel-btn" onClick={handleEat}>
            <ItemIcon itemDef={{icon: 'images/ui_icons/eat.png'}} maxWidth={18} />
            {getText("Eat")}
        </div>
    );
};

const EquipButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    const [ isEquipFailed, setIsEquipFailed ] = React.useState(false);
    const equipItem = useEquipmentStore(state => state.equipItem);
    const itemStacks = useRootStore(state => state.itemStacks);
    const itemDef = props.selectedStack.itemDef;

    // Handle equip button click
    const handleEquip = () => {
        if (itemDef.type.id === 'Equipable' && itemDef.slotType) {
            // Find the item in inventory
            const itemStack = itemStacks.find(stack =>
                stack && stack.itemId === itemDef.id
            );

            if (itemStack) {
                const success = equipItem(itemStack, itemDef.slotType.id);
                if (!success) {
                    setIsEquipFailed(true);
                } else {
                    props.onClose();
                }
            }
        }
    };
    return (
        <>
            <div className="panel-btn" onClick={handleEquip}>
                <ItemIcon itemDef={{icon: 'images/ui_icons/tshirt.png'}} maxWidth={18} />
                {getText("Equip")}
            </div>
            <MacOSModal
                title={getText('Equip Failed')}
                isOpen={isEquipFailed}
                onClose={props.onClose}
                initialSize={{ width: 600, height: 700 }}
                // portalId={interactionModalId.current}
            >
                {getText("You already equipped an item in this slot.")}
            </MacOSModal>
        </>
    );
};

const PickUpButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    const addStackToInventory = useRootStore(state => state.addStackToInventory);
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    const removeStackByUuid = useGroundStore(state => state.removeStackByUuid);

    const handlePickUp = () => {
        console.log("handlePickUp", props.selectedStack);
        addStackToInventory(props.selectedStack);
        removeStackByUuid(currRegionIndex, props.selectedStack.uuid);
        props.onClose();
    }

    return (
        <div className="panel-btn" onClick={handlePickUp}>
            <ItemIcon itemDef={{icon: 'images/ui_icons/pick_up.png'}} maxWidth={18} />
            {getText("Pick Up")}
        </div>
    );
};

const DropButton = (props: {
    selectedStack: InventoryItemStack,
    onClose: () => void
}) => {
    const removeSelectedInventoryStackByUuid = useRootStore(state => state.removeSelectedInventoryStackByUuid);
    const currRegionIndex = useRootStore(state => state.currRegionIndex);
    const addStackToGroundStacks = useGroundStore(state => state.addStackToGroundStacks);
    const currentLocationBuildingUuid = useLocationStore(state => state.currentLocationBuildingUuid);

    const handleDrop = () => {
        if (currentLocationBuildingUuid) {
            console.log("Dropping in building", currentLocationBuildingUuid);
        } else {
            console.log("Dropping on ground");
            addStackToGroundStacks(currRegionIndex, props.selectedStack);
        }
        removeSelectedInventoryStackByUuid(props.selectedStack.uuid);
        props.onClose();
    }

    return (
        <button className="panel-btn" onClick={handleDrop}>
            <ItemIcon itemDef={{icon: 'images/ui_icons/drop.png'}} maxWidth={18} />
            {getText("Drop")}
        </button>
    );
};
