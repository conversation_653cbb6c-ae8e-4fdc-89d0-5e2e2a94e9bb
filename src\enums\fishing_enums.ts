// @ts-check

import { getText } from "../i18n";
import { RARITY, ResourceTypes } from "./common_enum";
import { Fish, Item } from "../Interfaces";
// import { Items } from "./resources";
import { MATERIALS } from "./Materials";

console.trace();
// console.log("Loading fishing_enums.js", Items);

const BASIC_FISHING_ROD = {
    id: 'Basic Fishing Rod',
    get name() { return getText("Basic Fishing Rod"); },
    get description() { return getText("A simple fishing rod made from basic materials"); },
    "type": ResourceTypes["TOOL"],
    icon: '🎣',
    "rarity": RARITY.COMMON,
    "isFishingRod": true,
    "ingredients": [
        { itemDef: MATERIALS.Log, quantity: 1 },
        { itemDef: MATERIALS.Bark, quantity: 1 },
    ],
    "time": 45
};

const IMPROVED_FISHING_ROD = {
    id: 'Improved Fishing Rod',
    get name() { return getText("Improved Fishing Rod"); },
    get description() { return getText("A better fishing rod with improved durability and catch rate"); },
    "type": ResourceTypes["TOOL"],
    icon: '🎣',
    "rarity": RARITY.UNCOMMON,
    "isFishingRod": true,
    "ingredients": [
        { itemDef: BASIC_FISHING_ROD, quantity: 1 },
        { itemDef: MATERIALS.Log, quantity: 1 },
    ],
    "time": 60
};

const PROFESSIONAL_FISHING_ROD = {
    id: 'Professional Fishing Rod',
    get name() { return getText("Professional Fishing Rod"); },
    get description() { return getText("A high-quality fishing rod capable of catching rare fish"); },
    "type": ResourceTypes["TOOL"],
    icon: '🎣',
    "rarity": RARITY.RARE,
    "isFishingRod": true,
    "ingredients": [
        { itemDef: IMPROVED_FISHING_ROD, quantity: 1 },
        { itemDef: MATERIALS.Bark, quantity: 1 },
    ],
    "time": 90
};

const MASTER_FISHING_ROD = {
    id: 'Master Fishing Rod',
    get name() { return getText("Master Fishing Rod"); },
    get description() { return getText("An exceptional fishing rod crafted by a master angler, capable of catching epic fish"); },
    "type": ResourceTypes.TOOL,
    icon: '🎣',
    "rarity": RARITY.EPIC,
    "isFishingRod": true,
    "ingredients": [
        { itemDef: PROFESSIONAL_FISHING_ROD, quantity: 1 },
        { itemDef: MATERIALS.Log, quantity: 1 },
    ],
    "time": 120
};

const LEGENDARY_FISHING_ROD = {
    id: 'Legendary Fishing Rod',
    get name() { return getText("Legendary Fishing Rod"); },
    get description() { return getText("A mythical fishing rod of unparalleled quality, capable of catching the most legendary fish"); },
    "type": ResourceTypes.TOOL,
    icon: '🎣',
    "rarity": RARITY.LEGENDARY,
    "isFishingRod": true,
    "ingredients": [
        { itemDef: MASTER_FISHING_ROD, quantity: 1 },
        { itemDef: MATERIALS.Log, quantity: 1 },
    ],
    "time": 180
};

const FISHING_NET = {
    id: 'Fishing Net',
    get name() { return getText("Fishing Net"); },
    get description() { return getText("A large net used for catching fish"); },
    "type": ResourceTypes["TOOL"],
    icon: '🕸️',
    "rarity": RARITY.COMMON,
    "isFishingRod": true,
    "ingredients": [
        { itemDef: MATERIALS.Log, quantity: 1 },
        { itemDef: MATERIALS.Bark, quantity: 1 },
    ],
    "time": 45
};

export const FISHING_RODS: { [key: string]: Item} = {
    "Basic Fishing Rod": BASIC_FISHING_ROD,
    "Improved Fishing Rod": IMPROVED_FISHING_ROD,
    "Professional Fishing Rod": PROFESSIONAL_FISHING_ROD,
    "Master Fishing Rod": MASTER_FISHING_ROD,
    "Legendary Fishing Rod": LEGENDARY_FISHING_ROD,
    "Fishing Net": FISHING_NET,
};



export const FISH: { [key: string]: Fish} = {
    // OCEAN fish
    'Lobster': {
        id: 'Lobster',
        get name() { return getText('Lobster'); },
        get description() { return getText('desc_Lobster'); },
        type: ResourceTypes.EDIBLE,
        icon: '🦞', 
        rarity: RARITY.RARE,
        food: 30, 
        energy: 2, 
        health: 6, 
        freshness: 3
    },
    'Cyan_Cod': {
        id: 'Cyan Cod',
        get name() { return getText('Cyan Cod'); },
        get description() { return getText('desc_Cyan Cod'); }, 
        type: ResourceTypes.EDIBLE,
        icon: 'images/fish/fish_cyan.svg',
        rarity: RARITY.COMMON,
        food: 20,
    },
    'Tuna': {
        id: 'Tuna',
        get name() { return getText('Tuna'); },
        type: ResourceTypes.EDIBLE,
        get description() { return getText('desc_Tuna'); },
        icon: '🐟',
        rarity: RARITY.UNCOMMON,
        food: 30,
        energy: 20,
        health: 5,
        freshness: 720 // 12 hours
    },
    'Mackerel': {
        id: 'Mackerel',
        get name() { return getText('Mackerel'); },
        get description() { return getText('desc_Mackerel'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.COMMON,
        food: 15, 
        energy: 10, 
        health: 3, 
        freshness: 480 // 8 hours
    },
    'Sardine': {
        id: 'Sardine',
        get name() { return getText('Sardine'); },
        get description() { return getText('desc_Sardine'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.COMMON,
        food: 10, 
        energy: 5, 
        health: 2, 
        freshness: 360 // 6 hours
    },
    'Squid': {
        id: 'Squid',
        get name() { return getText('Squid'); },
        get description() { return getText('desc_Squid'); },
        type: ResourceTypes.EDIBLE,
        icon: '🦑', 
        rarity: RARITY.RARE,
        food: 35, 
        energy: 25, 
        health: 6, 
        freshness: 240 // 4 hours
    },
    'Swordfish': {
        id: 'Swordfish',
        get name() { return getText('Swordfish'); },
        get description() { return getText('desc_Swordfish'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐡', 
        rarity: RARITY.RARE,
        food: 40, 
        energy: 30, 
        health: 8, 
        freshness: 600 // 10 hours
    },
    'Giant Octopus': {
        id: 'Giant Octopus',
        get name() { return getText('Giant Octopus'); },
        get description() { return getText('A massive octopus with incredible intelligence that lurks in the deep ocean'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐙', 
        rarity: RARITY.EPIC,
        food: 45, 
        energy: 35, 
        health: 10, 
        freshness: 480 // 8 hours
    },
    'Kraken': {
        id: 'Kraken',
        get name() { return getText('Kraken'); },
        get description() { return getText('A legendary sea monster of enormous size, said to be able to pull entire ships underwater'); },
        type: ResourceTypes.EDIBLE,
        icon: '🦑', 
        rarity: RARITY.LEGENDARY,
        food: 65, 
        energy: 55, 
        health: 15, 
        freshness: 720 // 12 hours
    },
    
    // LAKE fish
    'Trout': {
        id: 'Trout',
        get name() { return getText('Trout'); },
        get description() { return getText('desc_Trout'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.UNCOMMON,
        food: 20, 
        energy: 15, 
        health: 4, 
        freshness: 300 // 5 hours
    },
    'Bass': {
        id: 'Bass',
        get name() { return getText('Bass'); },
        get description() { return getText('desc_Bass'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.COMMON,
        food: 15, 
        energy: 10, 
        health: 3, 
        freshness: 360 // 6 hours
    },
    'Carp': {
        id: 'Carp',
        get name() { return getText('Carp'); },
        get description() { return getText('desc_Carp'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.COMMON,
        food: 12, 
        energy: 8, 
        health: 2, 
        freshness: 480 // 8 hours
    },
    'Perch': {
        id: 'Perch',
        get name() { return getText('Perch'); },
        get description() { return getText('desc_Perch'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.UNCOMMON,
        food: 18, 
        energy: 12, 
        health: 3, 
        freshness: 360 // 6 hours
    },
    'Albino Catfish': {
        id: 'Albino Catfish',
        get name() { return getText('Albino Catfish'); },
        get description() { return getText('A rare white catfish that has lived for decades in the depths of the lake'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.EPIC,
        food: 42, 
        energy: 32, 
        health: 9, 
        freshness: 540 // 9 hours
    },
    'Lake Monster': {
        id: 'Lake Monster',
        get name() { return getText('Lake Monster'); },
        get description() { return getText('A legendary creature that has been rumored to inhabit this lake for centuries'); },
        type: ResourceTypes.EDIBLE,
        icon: '🦕', 
        rarity: RARITY.LEGENDARY,
        food: 58, 
        energy: 48, 
        health: 14, 
        freshness: 720 // 12 hours
    },
    
    // MARSH fish
    'Catfish': {
        id: 'Catfish',
        get name() { return getText('Catfish'); },
        get description() { return getText('desc_Catfish'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.UNCOMMON,
        food: 18, 
        energy: 12, 
        health: 3, 
        freshness: 420 // 7 hours
    },
    'Eel': {
        id: 'Eel',
        get name() { return getText('Eel'); },
        get description() { return getText('desc_Eel'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐍', 
        rarity: RARITY.RARE,
        food: 25, 
        energy: 20, 
        health: 5, 
        freshness: 240 // 4 hours
    },
    'Frog': {
        id: 'Frog',
        get name() { return getText('Frog'); },
        get description() { return getText('desc_Frog'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐸', 
        rarity: RARITY.COMMON,
        food: 8, 
        energy: 5, 
        health: 1, 
        freshness: 180 // 3 hours
    },
    'Snakehead': {
        id: 'Snakehead',
        get name() { return getText('Snakehead'); },
        get description() { return getText('desc_Snakehead'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.RARE,
        food: 22, 
        energy: 18, 
        health: 4, 
        freshness: 300 // 5 hours
    },
    'Swamp Dragon': {
        id: 'Swamp Dragon',
        get name() { return getText('Swamp Dragon'); },
        get description() { return getText('A massive amphibious creature with scales and fins that hunts in murky waters'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐊', 
        rarity: RARITY.EPIC,
        food: 44, 
        energy: 34, 
        health: 9, 
        freshness: 480 // 8 hours
    },
    'Marsh Leviathan': {
        id: 'Marsh Leviathan',
        get name() { return getText('Marsh Leviathan'); },
        get description() { return getText('A legendary creature of immense size that has adapted to life in the marshlands'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐉', 
        rarity: RARITY.LEGENDARY,
        food: 62, 
        energy: 52, 
        health: 15, 
        freshness: 720 // 12 hours
    },
    
    // BEACH creatures
    'Crab': {
        id: 'Crab',
        get name() { return getText('Crab'); },
        get description() { return getText('desc_Crab'); },
        type: ResourceTypes.EDIBLE,
        icon: 'images/fish/crab.png', 
        rarity: RARITY.COMMON,
        food: 12, 
        energy: 8, 
        health: 2,
        useTimeModifier: 2,
        freshness: 240 // 4 hours
    },
    'Clam': {
        id: 'Clam',
        get name() { return getText('Clam'); },
        get description() { return getText('desc_Clam'); },
        type: ResourceTypes.EDIBLE,
        icon: 'images/fish/clam.png', 
        rarity: RARITY.COMMON,
        food: 8, 
        energy: 1, 
        health: 1, 
        freshness: 2
    },
    'Shrimp': {
        id: 'Shrimp',
        get name() { return getText('Shrimp'); },
        get description() { return getText('desc_Shrimp'); },
        type: ResourceTypes.EDIBLE,
        icon: '🦐', 
        rarity: RARITY.UNCOMMON,
        food: 15, 
        energy: 10, 
        health: 3, 
        freshness: 120 // 2 hours
    },
    'Giant Crab': {
        id: 'Giant Crab',
        get name() { return getText('Giant Crab'); },
        get description() { return getText('An enormous crab with a shell as hard as steel and claws that can snap fishing lines'); },
        type: ResourceTypes.EDIBLE,
        icon: '🦀', 
        rarity: RARITY.EPIC,
        food: 40, 
        energy: 30, 
        health: 8, 
        freshness: 360 // 6 hours
    },
    'Pearl Oyster': {
        id: 'Pearl Oyster',
        get name() { return getText('Pearl Oyster'); },
        get description() { return getText('A legendary oyster containing a pearl of immense value and beauty'); },
        type: ResourceTypes.EDIBLE,
        icon: '🦪', 
        rarity: RARITY.LEGENDARY,
        food: 55, 
        energy: 45, 
        health: 12, 
        freshness: 240 // 4 hours
    },
    
    // RIVER fish
    'Salmon': {
        id: 'Salmon',
        get name() { return getText('Salmon'); },
        get description() { return getText('desc_Salmon'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.UNCOMMON,
        food: 25, 
        energy: 20, 
        health: 5, 
        freshness: 360 // 6 hours
    },
    'River Serpent': {
        id: 'River Serpent',
        get name() { return getText('River Serpent'); },
        get description() { return getText('A rare snake-like fish with vibrant colors that dwells in fast-flowing rivers'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐍', 
        rarity: RARITY.EPIC,
        food: 46, 
        energy: 36, 
        health: 10, 
        freshness: 480 // 8 hours
    },
    'Golden Koi': {
        id: 'Golden Koi',
        get name() { return getText('Golden Koi'); },
        get description() { return getText('A legendary fish of pure gold, said to grant immortality to those who catch it'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐠',
        rarity: RARITY.LEGENDARY,
        food: 60, 
        energy: 50, 
        health: 15, 
        freshness: 720 // 12 hours
    },
    'Pike': {
        id: 'Pike',
        get name() { return getText('Pike'); },
        get description() { return getText('desc_Pike'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.UNCOMMON,
        food: 22, 
        energy: 18, 
        health: 4, 
        freshness: 300 // 5 hours
    },
    'Sturgeon': {
        id: 'Sturgeon',
        get name() { return getText('Sturgeon'); },
        get description() { return getText('desc_Sturgeon'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.RARE,
        food: 35, 
        energy: 28, 
        health: 7, 
        freshness: 420 // 7 hours
    },
    'Minnow': {
        id: 'Minnow',
        get name() { return getText('Minnow'); },
        get description() { return getText('desc_Minnow'); },
        type: ResourceTypes.EDIBLE,
        icon: '🐟', 
        rarity: RARITY.COMMON,
        food: 5, 
        energy: 3, 
        health: 1, 
        freshness: 120 // 2 hours
    },
    "SeaCucumber": {
        id: "SeaCucumber",
        get name() { return getText('Sea Cucumber'); },
        get description() { return getText('desc_SeaCucumber'); },
        "type": ResourceTypes.EDIBLE,
        "icon": "🥒",
        "food": 15,
        "water": 5,
        "energy": 10,
        "health": 3,
        freshness: 100,
        fishVal: 0.5
    },
};


export const FISH_BY_BIOME = {
    'OCEAN': [FISH.Tuna, FISH.Mackerel, FISH.Sardine, FISH.Squid, FISH.Swordfish, FISH['Giant Octopus'], FISH.Kraken, FISH.Lobster],
    'LAKE': [FISH.Trout, FISH.Bass, FISH.Carp, FISH.Perch, FISH['Albino Catfish'], FISH['Lake Monster']],
    'MARSH': [FISH.Catfish, FISH.Eel, FISH.Frog, FISH.Snakehead, FISH['Swamp Dragon'], FISH['Marsh Leviathan']],
    'BEACH': [FISH.Crab, FISH.Clam, FISH.Shrimp, FISH['Giant Crab'], FISH['Pearl Oyster']],
    'RIVER': [FISH.Salmon, FISH.Pike, FISH.Sturgeon, FISH.Minnow, FISH['River Serpent'], FISH['Golden Koi']]
};
