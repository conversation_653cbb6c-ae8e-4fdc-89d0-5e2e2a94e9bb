import React, { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { createPortal } from "react-dom";
import { ItemDescriptionPanel } from "./ItemDescriptionPanel";
import { InventoryItemStack, Item } from "src/Interfaces";
import { ItemLocation } from "src/enums/common_enum";

interface ItemDescriptionPopoverProps {
  selectedStack?: InventoryItemStack;
  anchorRect?: DOMRect | null;
  isOpen: boolean;
  onClose: () => void;
  location?: ItemLocation;
  itemDef?: Item;
}

const arrowPosition = { top: '96%', left: '50%' }

export const ItemDescriptionPopover: React.FC<ItemDescriptionPopoverProps> = ({
  selectedStack,
  anchorRect,
  isOpen,
  onClose,
  location,
  itemDef
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  // const [arrowPosition, setArrowPosition] = useState({ top: '50%', left: '50%' });

  const [arrowDirection, setArrowDirection] = useState<'top' | 'right' | 'bottom' | 'left'>('top');
  const [portalElement, setPortalElement] = useState<HTMLElement | null>(null);

  // Create portal element on mount
  useEffect(() => {
    // Check if portal element already exists
    let element = document.getElementById('popover-portal');
    if (!element) {
      element = document.createElement('div');
      element.id = 'popover-portal';
      document.body.appendChild(element);
    }
    setPortalElement(element);

    return () => {
      // Only remove if we created it and it's still in the DOM
      if (element && element.parentElement && element.childNodes.length === 0) {
        document.body.removeChild(element);
      }
    };
  }, []);

  // Calculate position based on the anchor element's position
  useEffect(() => {
    if (!anchorRect || !isOpen) return;

    const calculatePosition = () => {
      // Force a direct position above the item, regardless of space constraints
      // This is a simplified approach to ensure the popover appears above the item

      // We need to wait for the popover to render first to get its dimensions
      setTimeout(() => {
        if (!popoverRef.current) return;

        const popoverWidth = popoverRef.current.offsetWidth;
        const popoverHeight = popoverRef.current.offsetHeight;

        // Position directly above the item
        const left = anchorRect.left + (anchorRect.width / 2) - (popoverWidth / 2);
        const top = anchorRect.top - popoverHeight - 10; // 10px gap

        // Set arrow to point directly at the item
        const arrowDir = 'bottom';

        console.log("Positioning popover:", {
          anchorRect,
          popoverSize: { width: popoverWidth, height: popoverHeight },
          position: { top, left }
        });

        setPosition({ top, left });
        // const arrowPos = { top: '100%', left: '50%' };
        // setArrowPosition(arrowPos);
        setArrowDirection(arrowDir);
      }, 10); // Slightly longer timeout to ensure DOM is ready
    };

    // Initial calculation
    calculatePosition();

    // Recalculate on window resize
    window.addEventListener('resize', calculatePosition);
    return () => window.removeEventListener('resize', calculatePosition);
  }, [anchorRect, isOpen]);

  // Handle click outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click was outside the popover
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        console.log("Click outside popover detected");
        onClose();
      }
    };

    // Add the event listener with a slight delay to avoid immediate triggering
    const timeoutId = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 100);

    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // // Log the position for debugging
  // useEffect(() => {
  //   if (isOpen && anchorRect) {
  //     console.log("Popover position:", {
  //       anchorRect,
  //       position,
  //       arrowDirection
  //     });
  //   }
  // }, [isOpen, anchorRect, position, arrowPosition, arrowDirection]);

  if ((!selectedStack && !itemDef) || !isOpen || !portalElement) return null;

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={popoverRef}
          className={`item-description-popover arrow-${arrowDirection}`}
          style={{
            position: 'fixed',
            top: position.top,
            left: position.left,
            zIndex: 9999
          }}
          initial={{ opacity: 0, scale: 0.95, y: -10 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: -10 }}
          transition={{ duration: 0.2, type: 'spring', damping: 15 }}
        >
          {/* Arrow element */}
          <div
            className="popover-arrow"
            style={{
              top: arrowPosition.top,
              left: arrowPosition.left
            }}
          />
          <ItemDescriptionPanel
            selectedStack={selectedStack}
            onClose={onClose}
            location={location}
            itemDef={selectedStack ? selectedStack.itemDef : itemDef}
          />
        </motion.div>
      )}
    </AnimatePresence>,
    portalElement
  );
};
