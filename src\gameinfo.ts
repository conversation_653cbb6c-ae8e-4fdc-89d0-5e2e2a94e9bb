import { realSecToGameMinRatio } from './settings.js';
import {
    baseWaterDrainRate, baseEnergyDrainRate, baseFoodDrainRate,
    basePlayerSpeed, baseSpoilSpeed
} from './settings.js';
import { updateWeather } from './weather_system.js';
import { PlacedBuilding } from './Interfaces.js';
import {
    getCurrentSeason, getDayOfSeason, getYear, getTimeOfDay,
    getTimeBarGradient, SeasonData, Season
} from './seasons.js';
import { getTextWithArgs } from './i18n.js';
import { showTooltip } from './util.js';


export const placedBuildingsInAllRegions = new Map<number, Map<string, PlacedBuilding>>();

interface Player {
    x: number;
    y: number;
    width: number;
    height: number;
    speed: number;
    color: string;
    hasSpawned: boolean;
}
export const player = {
    x: 0,
    y: 0,
    width: 10,
    height: 10,
    speed: basePlayerSpeed, // in game movement units per ingame minute
    color: "red",
    hasSpawned: false
};

export const gameStatus = {
    health: 100,
    food: 100,
    water: 100,
    energy: 100,
    // time: {
    //     days: 1,
    //     hours: 9,
    //     minutes: 0,
    //     seconds: 0,
    //     elapsedSteps: 0
    // },
    element: null,
    hasBoat: false
};

// Make gameStatus available globally
window.gameStatus = gameStatus;

export const gameTime = {
    days: 119,
    hours: 9,
    minutes: 0,
    seconds: 0,
    season: null as SeasonData | null,
    timeOfDay: 'day' as 'day' | 'dusk' | 'night',
}


const ElementsCache = {
    dayDisplay: null,
    seasonDisplay: null,
    timeBarContainer: null,
    timeBar: null,
    timeIndicator: null,
    timeDisplay: null,
    health: null,
    food: null,
    water: null,
    energy: null,
}

export const initTimeUI = () => {
    ElementsCache.dayDisplay = document.getElementById('day-display');
    ElementsCache.seasonDisplay = document.getElementById('season-display');
    ElementsCache.timeBarContainer = document.getElementById('time-bar-container');
    ElementsCache.timeBar = document.getElementById('time-bar');
    ElementsCache.timeIndicator = document.getElementById('time-indicator');
    ElementsCache.timeDisplay = document.getElementById('time-display');

    ElementsCache.health = document.getElementById('health-bar');
    ElementsCache.food = document.getElementById('food-bar');
    ElementsCache.water = document.getElementById('water-bar');
    ElementsCache.energy = document.getElementById('energy-bar');

    updateDayAndSeason();
    updateGameStatusUI();
}


/**
 * @param {string} title
 * @param {number} actionDurationInGameMin
 * @param {{ (): void; (): void; (): void; (): void; (): void; }} doneCallback
 */
export function startProgressBar(title, actionDurationInGameMin, doneCallback) {
    // const overlay = document.querySelector('#progress-overlay');
    // overlay.style.display = 'block';

    // Create progress bar overlay
    const overlay = document.createElement('div');
    overlay.id = 'progress-overlay';
    overlay.innerHTML = `
        <div class="progress-container">
            <div class="progress-text">${title}...</div>
            <div class="progress-bar-container">
                <div id="progress-bar"></div>
            </div>
        </div>
    `;
    document.body.appendChild(overlay);

    const progressBar = document.getElementById('progress-bar');

    // const actionDurationInGame = 60; // 1 hour in game
    const actionMsInReal = 1000 * actionDurationInGameMin / realSecToGameMinRatio; // 6 seconds in real life

    let startMs;
    let previousMs;

    const updateProgress = (currentMs) => {
        if (startMs === undefined) {
            startMs = currentMs;
        }
        if (previousMs === undefined) {
            previousMs = currentMs;
        }

        const elapseMsThisFrame = currentMs - previousMs;
        const totalMsElapsed = currentMs - startMs;

        const progress = Math.min(totalMsElapsed / actionMsInReal * 100, 100);
        progressBar.style.width = `${progress}%`;
        // console.log("progress: ", progress);

        if (progress < 100) {
            const deltaSec = elapseMsThisFrame / 1000;
            updateTime(deltaSec);
            previousMs = currentMs;
            requestAnimationFrame(updateProgress);
        } else {
            const extraMs = totalMsElapsed - actionMsInReal;
            const deltaSec = (elapseMsThisFrame - extraMs) / 1000;
            updateTime(deltaSec);
            // Small delay to ensure the progress bar animation completes
            setTimeout(() => {
                // Remove overlay
                document.body.removeChild(overlay);
                console.log("doneCallback");
                doneCallback();
            }, 100);
        }
    };

    requestAnimationFrame(updateProgress);
}


function updateTime(deltaSec) {
    const inGameMin = deltaSec * realSecToGameMinRatio;
    updateTimeWithIngameMin(inGameMin);
}

export function updateTimeWithIngameMin(inGameMin: number): void {
    gameTime.minutes += inGameMin;
    let dayChanged = false;

    // Make sure gameTime.season is initialized
    if (!gameTime.season) {
        gameTime.season = getCurrentSeason(gameTime.days);
    }

    while (gameTime.minutes >= 60) {
        gameTime.minutes -= 60;
        gameTime.hours++;

        if (gameTime.hours >= 24) {
            gameTime.hours = 0;
            gameTime.days++;
            dayChanged = true;
        }

        // Update time of day when hour changes
        gameTime.timeOfDay = getTimeOfDay(gameTime.hours, gameTime.season);
    }

    // Update season if day changed
    if (dayChanged && gameTime.days > 0) {
        updateDayAndSeason();
    }

    // Decrease status values
    gameStatus.food = Math.max(0, gameStatus.food - baseFoodDrainRate * inGameMin);
    gameStatus.water = Math.max(0, gameStatus.water - baseWaterDrainRate * inGameMin);
    gameStatus.energy = Math.max(0, gameStatus.energy - baseEnergyDrainRate * inGameMin);

    // Apply day/night effects
    applyTimeOfDayEffects(gameTime.timeOfDay);

    updateGameStatusUI();
    updateWeather(inGameMin);
}

const updateDayAndSeason = () => {
    gameTime.season = getCurrentSeason(gameTime.days);
    // Get time of day with new season data
    gameTime.timeOfDay = getTimeOfDay(gameTime.hours, gameTime.season);

    // Update time bar gradient percentage when season changes
    ElementsCache.timeBar.style.background = getTimeBarGradient(gameTime.season);

    // Update day display with season info
    // const year = getYear(gameTime.days);
    ElementsCache.dayDisplay.textContent = `Day ${gameTime.days}`;

    // Update season display
    // const seasonInfoIcon = document.createElement('span');
    // seasonInfoIcon.className = 'season-info-icon';
    // seasonInfoIcon.innerHTML = 'ⓘ';
    // seasonInfoIcon.style.cssText = `
    //     margin-left: 6px;
    //     font-size: 12px;
    // `;

    ElementsCache.seasonDisplay.innerHTML = `<img src = "${gameTime.season.icon}" style="width: 16px; height: 16px;" /> ${gameTime.season.name}`;

    ElementsCache.seasonDisplay.addEventListener('click', (e) =>{
        const dayOfSeason = getDayOfSeason(gameTime.days);
        const daysLeft = gameTime.season.daysInSeason - dayOfSeason + 1;
        showTooltip(e, ElementsCache.seasonDisplay, getTextWithArgs("seasonDaysLeft", { day: daysLeft }));
    });
  
};


/**
 * Apply visual effects based on time of day
 * @param timeOfDay Current time of day ('day', 'dusk', or 'night')
 */
function applyTimeOfDayEffects(timeOfDay: 'day' | 'dusk' | 'night'): void {
    // Get the canvas element
    const canvas = document.getElementById('gameCanvas') as HTMLCanvasElement;
    if (!canvas) return;

    // Apply different filters based on time of day
    switch (timeOfDay) {
        case 'day':
            // Normal daylight - no filter
            canvas.style.filter = 'brightness(1)';
            break;
        case 'dusk':
            // Dusk - slightly darker with orange tint
            canvas.style.filter = 'brightness(0.85) sepia(0.3)';
            break;
        case 'night':
            // Night - darker with blue tint
            canvas.style.filter = 'brightness(0.6) saturate(0.8)';
            break;
    }
}

const totalMinutesInDay = 24 * 60;
export function updateGameStatusUI(): void {
    if (!gameStatus.element) return;

    // Update status bars
    ['health', 'food', 'water', 'energy'].forEach(stat => {
        ElementsCache[stat].style.width = `${gameStatus[stat]}%`;
    });


    // Calculate position of indicator (0-100%)
    // A full day is 24 hours, so calculate percentage through the day
    const currentMinutes = gameTime.hours * 60 + gameTime.minutes;
    const dayPercentage = (currentMinutes / totalMinutesInDay) * 100;

    // Get the width of the time bar to calculate exact pixel position
    const timeBarWidth = ElementsCache.timeBar.offsetWidth;
    const pixelPosition = (dayPercentage / 100) * timeBarWidth;

    // Update indicator position directly with pixels for smoother movement
    ElementsCache.timeIndicator.style.left = `${pixelPosition}px`;

    // Update digital time display
    const hours = gameTime.hours.toString().padStart(2, '0');
    const minutes = Math.floor(gameTime.minutes).toString().padStart(2, '0');
    ElementsCache.timeDisplay.textContent = `${hours}:${minutes}`;
}
